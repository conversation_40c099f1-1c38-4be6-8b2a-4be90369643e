substitutions:
    update_interval: 1s
    ds18b20_pin: GPIO6
    tds_pin: GPIO3

one_wire:
    - platform: gpio
      pin: $ds18b20_pin

sensor:
    - platform: dallas_temp
      id: water_temperature
      name: 'Water Temperature'
      accuracy_decimals: 1
      update_interval: $update_interval

    - platform: adc
      id: water_tds_voltage
      pin: $tds_pin
      internal: true
      accuracy_decimals: 1
      update_interval: $update_interval

    - platform: template
      id: water_temperature_correction
      internal: true
      accuracy_decimals: 1
      update_interval: $update_interval
      lambda: 'return 1.0 + (0.02 * (id(water_temperature).state - 25.0));'

    - platform: template
      id: water_tds_voltage_correction
      internal: true
      accuracy_decimals: 1
      update_interval: $update_interval
      lambda: 'return id(water_temperature_correction).state * id(water_tds_voltage).state;'

    - platform: template
      id: water_tds
      name: 'Water TDS'
      unit_of_measurement: 'ppm'
      update_interval: $update_interval
      accuracy_decimals: 0
      lambda: 'return (66.71 * pow(id(water_tds_voltage_correction).state, 3)) - (127.93 * pow(id(water_tds_voltage_correction).state, 2)) + (428.7 * id(water_tds_voltage_correction).state);'
